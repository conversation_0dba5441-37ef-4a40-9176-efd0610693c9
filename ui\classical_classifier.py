"""
经典分类器检测模块
支持多种传统机器学习算法进行轴承故障分类检测
"""

import os
import sys
import numpy as np
import pandas as pd
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QScrollArea, QFrame, QSplitter, QProgressBar, QMessageBox, QFileDialog,
    QGroupBox, QDialog
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal

# 算法模块导入
from algorithms import create_classical_classifier

# 导入配置对话框
from ui.classifier_config_dialog import ClassifierConfigDialog
from ui.training_results_dialog import TrainingResultsDialog

from ui.styles import (
    PRIMARY_BG, SECONDARY_BG, ACCENT_COLOR, TEXT_PRIMARY, TEXT_SECONDARY,
    SUCCESS_COLOR, WARNING_COLOR, ERROR_COLOR, INFO_COLOR, HIGHLIGHT_COLOR
)


class ModelTrainingThread(QThread):
    """模型训练线程"""
    progress_updated = pyqtSignal(int, str)
    training_completed = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)

    def __init__(self, X, y, classifier_type, params, test_size=0.2):
        super().__init__()
        self.X = X
        self.y = y
        self.classifier_type = classifier_type
        self.params = params
        self.test_size = test_size
        self.classifier = create_classical_classifier()
        
    def run(self):
        """执行模型训练"""
        try:
            # 进度回调函数
            def progress_callback(progress, message):
                self.progress_updated.emit(progress, message)

            # 准备数据
            self.progress_updated.emit(10, "准备数据...")
            X_train, X_test, y_train, y_test = self.classifier.prepare_data(
                self.X, self.y, test_size=self.test_size
            )

            # 训练模型
            training_result = self.classifier.train(
                X_train, y_train,
                algorithm=self.classifier_type,
                config=self.params,
                progress_callback=progress_callback
            )

            # 评估模型
            self.progress_updated.emit(90, "评估模型...")
            evaluation_result = self.classifier.evaluate(X_test, y_test)

            # 获取特征重要性
            feature_importance = self.classifier.get_feature_importance()

            # 合并结果
            results = {
                'classifier': self.classifier,
                'training_result': training_result,
                'evaluation_result': evaluation_result,
                'feature_importance': feature_importance
            }

            self.training_completed.emit(results)

        except Exception as e:
            self.error_occurred.emit(str(e))


class ClassicalClassifier(QWidget):
    """经典分类器检测界面"""

    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.current_classifier = None
        self.feature_data = None
        self.current_config = None

        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题
        title_label = QLabel("经典分类器监测")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 28px;
                font-weight: bold;
                color: {ACCENT_COLOR};
                margin-bottom: 10px;
            }}
        """)
        layout.addWidget(title_label)

        # 创建主分割器
        main_splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(main_splitter)

        # 左侧控制面板
        self.create_control_panel(main_splitter)

        # 右侧结果显示区域
        self.create_result_panel(main_splitter)

        # 设置分割器比例
        main_splitter.setSizes([350, 850])
        
    def create_control_panel(self, parent):
        """创建左侧控制面板"""
        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)
        control_layout.setContentsMargins(15, 15, 15, 15)
        control_layout.setSpacing(20)

        # 数据源选择
        data_group = QGroupBox("数据源选择")
        data_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: 18px;
                font-weight: bold;
                color: {ACCENT_COLOR};
                border: 2px solid {SECONDARY_BG};
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 15px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
            }}
        """)
        data_layout = QVBoxLayout(data_group)
        data_layout.setSpacing(15)

        self.load_data_btn = QPushButton("加载特征数据")
        self.load_data_btn.clicked.connect(self.load_feature_data)
        self.load_data_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {INFO_COLOR};
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 15px;
                border-radius: 10px;
                border: none;
            }}
            QPushButton:hover {{
                background-color: #5dade2;
            }}
            QPushButton:pressed {{
                background-color: #3498db;
            }}
        """)
        data_layout.addWidget(self.load_data_btn)

        self.data_status_label = QLabel("已加载: 1000 条数据")
        self.data_status_label.setStyleSheet(f"""
            QLabel {{
                color: {SUCCESS_COLOR};
                font-size: 16px;
                font-weight: bold;
                padding: 10px;
                background-color: {SECONDARY_BG};
                border-radius: 8px;
            }}
        """)
        data_layout.addWidget(self.data_status_label)

        control_layout.addWidget(data_group)

        # 配置按钮
        config_group = QGroupBox("配置管理")
        config_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: 18px;
                font-weight: bold;
                color: {ACCENT_COLOR};
                border: 2px solid {SECONDARY_BG};
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 15px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
            }}
        """)
        config_layout = QVBoxLayout(config_group)
        config_layout.setSpacing(15)

        self.config_btn = QPushButton("特征选择与分类器配置")
        self.config_btn.clicked.connect(self.open_config_dialog)
        self.config_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ACCENT_COLOR};
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 15px;
                border-radius: 10px;
                border: none;
            }}
            QPushButton:hover {{
                background-color: {HIGHLIGHT_COLOR};
            }}
            QPushButton:pressed {{
                background-color: #2980b9;
            }}
        """)
        config_layout.addWidget(self.config_btn)

        # 配置状态显示
        self.config_status_label = QLabel("训练失败")
        self.config_status_label.setStyleSheet(f"""
            QLabel {{
                color: {WARNING_COLOR};
                font-size: 16px;
                font-weight: bold;
                padding: 10px;
                background-color: {SECONDARY_BG};
                border-radius: 8px;
            }}
        """)
        config_layout.addWidget(self.config_status_label)

        control_layout.addWidget(config_group)

        # 操作按钮
        button_group = QGroupBox("模型操作")
        button_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: 18px;
                font-weight: bold;
                color: {ACCENT_COLOR};
                border: 2px solid {SECONDARY_BG};
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 15px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
            }}
        """)
        button_layout = QVBoxLayout(button_group)
        button_layout.setSpacing(15)

        self.train_btn = QPushButton("训练模型")
        self.train_btn.clicked.connect(self.train_model)
        self.train_btn.setEnabled(False)
        self.train_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {SUCCESS_COLOR};
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 15px;
                border-radius: 10px;
                border: none;
            }}
            QPushButton:hover {{
                background-color: #27ae60;
            }}
            QPushButton:disabled {{
                background-color: {TEXT_SECONDARY};
                color: #7f8c8d;
            }}
        """)
        button_layout.addWidget(self.train_btn)

        self.save_model_btn = QPushButton("保存模型")
        self.save_model_btn.clicked.connect(self.save_model)
        self.save_model_btn.setEnabled(False)
        self.save_model_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {WARNING_COLOR};
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 15px;
                border-radius: 10px;
                border: none;
            }}
            QPushButton:hover {{
                background-color: #f39c12;
            }}
            QPushButton:disabled {{
                background-color: {TEXT_SECONDARY};
                color: #7f8c8d;
            }}
        """)
        button_layout.addWidget(self.save_model_btn)

        self.load_model_btn = QPushButton("加载模型")
        self.load_model_btn.clicked.connect(self.load_model)
        self.load_model_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {INFO_COLOR};
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 15px;
                border-radius: 10px;
                border: none;
            }}
            QPushButton:hover {{
                background-color: #5dade2;
            }}
        """)
        button_layout.addWidget(self.load_model_btn)

        control_layout.addWidget(button_group)

        # 添加弹性空间
        control_layout.addStretch()

        parent.addWidget(control_widget)

    def open_config_dialog(self):
        """打开配置对话框"""
        dialog = ClassifierConfigDialog(self)
        dialog.config_changed.connect(self.on_config_changed)

        if dialog.exec_() == QDialog.Accepted:
            self.current_config = dialog.get_config()
            self.update_config_status()

    def on_config_changed(self, config):
        """配置变更处理"""
        self.current_config = config
        self.update_config_status()

    def update_config_status(self):
        """更新配置状态显示"""
        if self.current_config:
            feature_count = len(self.current_config['selected_features'])
            classifier_type = self.current_config['classifier_type']
            status_text = f"已配置: {classifier_type}, {feature_count}类特征"
            self.config_status_label.setText(status_text)
            self.config_status_label.setStyleSheet(f"""
                QLabel {{
                    color: {SUCCESS_COLOR};
                    font-size: 16px;
                    font-weight: bold;
                    padding: 10px;
                    background-color: {SECONDARY_BG};
                    border-radius: 8px;
                }}
            """)
            # 如果有数据和配置，启用训练按钮
            if self.feature_data is not None:
                self.train_btn.setEnabled(True)
        else:
            self.config_status_label.setText("未配置")
            self.config_status_label.setStyleSheet(f"""
                QLabel {{
                    color: {WARNING_COLOR};
                    font-size: 16px;
                    font-weight: bold;
                    padding: 10px;
                    background-color: {SECONDARY_BG};
                    border-radius: 8px;
                }}
            """)

    def create_result_panel(self, parent):
        """创建右侧结果显示面板"""
        result_widget = QWidget()
        self.result_layout = QVBoxLayout(result_widget)
        self.result_layout.setContentsMargins(15, 15, 15, 15)
        self.result_layout.setSpacing(20)

        # 训练状态
        status_group = QGroupBox("训练状态")
        status_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: 18px;
                font-weight: bold;
                color: {ACCENT_COLOR};
                border: 2px solid {SECONDARY_BG};
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 15px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
            }}
        """)
        status_layout = QVBoxLayout(status_group)
        status_layout.setSpacing(15)

        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid {SECONDARY_BG};
                border-radius: 8px;
                text-align: center;
                font-size: 14px;
                font-weight: bold;
                color: {TEXT_PRIMARY};
                background-color: {SECONDARY_BG};
            }}
            QProgressBar::chunk {{
                background-color: {SUCCESS_COLOR};
                border-radius: 6px;
            }}
        """)
        status_layout.addWidget(self.progress_bar)

        self.status_label = QLabel("训练失败")
        self.status_label.setStyleSheet(f"""
            QLabel {{
                color: {WARNING_COLOR};
                font-size: 18px;
                font-weight: bold;
                padding: 15px;
                background-color: {SECONDARY_BG};
                border-radius: 8px;
                border: 2px solid {WARNING_COLOR};
            }}
        """)
        status_layout.addWidget(self.status_label)

        self.result_layout.addWidget(status_group)

        # 添加一个简单的提示标签
        info_label = QLabel("训练完成后将自动打开结果详情窗口")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet(f"""
            QLabel {{
                font-size: 16px;
                color: {TEXT_SECONDARY};
                padding: 30px;
                background-color: {SECONDARY_BG};
                border-radius: 10px;
                border: 2px dashed {TEXT_SECONDARY};
            }}
        """)

        self.result_layout.addWidget(info_label)
        parent.addWidget(result_widget)



    def load_feature_data(self):
        """加载特征数据"""
        try:
            # 这里应该从数据库加载特征数据
            # 暂时使用示例数据
            QMessageBox.information(self, "提示", "特征数据加载功能需要连接到特征提取模块")

            # 示例数据生成（实际应用中应从数据库读取）
            np.random.seed(42)
            n_samples = 1000
            n_features = 20

            # 生成示例特征数据
            X = np.random.randn(n_samples, n_features)
            # 生成示例标签（正常、内圈故障、外圈故障、滚动体故障）
            y = np.random.choice(['正常', '内圈故障', '外圈故障', '滚动体故障'], n_samples)

            self.feature_data = pd.DataFrame(X, columns=[f'特征_{i+1}' for i in range(n_features)])
            self.feature_data['标签'] = y

            self.data_status_label.setText(f"已加载: {len(self.feature_data)} 条数据")
            self.data_status_label.setStyleSheet(f"""
                QLabel {{
                    color: {SUCCESS_COLOR};
                    font-size: 16px;
                    font-weight: bold;
                    padding: 10px;
                    background-color: {SECONDARY_BG};
                    border-radius: 8px;
                }}
            """)

            # 如果有配置，启用训练按钮
            if self.current_config is not None:
                self.train_btn.setEnabled(True)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载数据失败: {str(e)}")

    def get_selected_features(self):
        """获取选中的特征"""
        if self.feature_data is None or self.current_config is None:
            return None, None

        # 根据配置筛选特征
        # 这里应该根据选中的特征类型筛选特征
        # 暂时返回所有特征
        X = self.feature_data.drop('标签', axis=1)
        y = self.feature_data['标签']

        return X, y

    def get_classifier_params(self):
        """获取分类器参数"""
        if self.current_config is None:
            return {}
        return self.current_config['classifier_params']

    def train_model(self):
        """训练模型"""
        try:
            # 检查配置
            if self.current_config is None:
                QMessageBox.warning(self, "警告", "请先进行特征选择和分类器配置")
                return

            # 获取特征数据
            X, y = self.get_selected_features()
            if X is None:
                QMessageBox.warning(self, "警告", "请先加载特征数据")
                return

            # 获取分类器参数
            classifier_type = self.current_config['classifier_type']
            params = self.get_classifier_params()
            test_size = self.current_config['test_size']

            # 显示训练状态
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.status_label.setText("开始训练...")
            self.status_label.setStyleSheet(f"""
                QLabel {{
                    color: {INFO_COLOR};
                    font-size: 18px;
                    font-weight: bold;
                    padding: 15px;
                    background-color: {SECONDARY_BG};
                    border-radius: 8px;
                    border: 2px solid {INFO_COLOR};
                }}
            """)
            self.train_btn.setEnabled(False)

            # 启动训练线程
            self.training_thread = ModelTrainingThread(
                X, y, classifier_type, params, test_size
            )
            self.training_thread.progress_updated.connect(self.update_training_progress)
            self.training_thread.training_completed.connect(self.on_training_completed)
            self.training_thread.error_occurred.connect(self.on_training_error)
            self.training_thread.start()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"训练失败: {str(e)}")
            self.train_btn.setEnabled(True)
            self.progress_bar.setVisible(False)

    def update_training_progress(self, value, message):
        """更新训练进度"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)

    def on_training_completed(self, results):
        """训练完成处理"""
        self.current_classifier = results['classifier']
        self.progress_bar.setVisible(False)
        self.status_label.setText("训练完成! 点击查看详细结果")
        self.status_label.setStyleSheet(f"""
            QLabel {{
                color: {SUCCESS_COLOR};
                font-size: 18px;
                font-weight: bold;
                padding: 15px;
                background-color: {SECONDARY_BG};
                border-radius: 8px;
                border: 2px solid {SUCCESS_COLOR};
            }}
        """)
        self.train_btn.setEnabled(True)
        self.save_model_btn.setEnabled(True)

        # 打开结果对话框
        self.show_results_dialog(results)

    def show_results_dialog(self, results):
        """显示结果对话框"""
        try:
            dialog = TrainingResultsDialog(results, self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"显示结果失败: {str(e)}")

    def on_training_error(self, error_message):
        """训练错误处理"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("训练失败")
        self.status_label.setStyleSheet(f"""
            QLabel {{
                color: {ERROR_COLOR};
                font-size: 18px;
                font-weight: bold;
                padding: 15px;
                background-color: {SECONDARY_BG};
                border-radius: 8px;
                border: 2px solid {ERROR_COLOR};
            }}
        """)
        self.train_btn.setEnabled(True)
        QMessageBox.critical(self, "训练错误", error_message)





    def save_model(self):
        """保存训练好的模型"""
        if self.current_classifier is None:
            QMessageBox.warning(self, "警告", "没有可保存的模型")
            return

        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存模型", "", "模型文件 (*.pkl);;所有文件 (*)"
            )

            if file_path:
                self.current_classifier.save_model(file_path)
                QMessageBox.information(self, "成功", "模型保存成功!")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存模型失败: {str(e)}")

    def load_model(self):
        """加载训练好的模型"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "加载模型", "", "模型文件 (*.pkl);;所有文件 (*)"
            )

            if file_path:
                self.current_classifier = create_classical_classifier()
                self.current_classifier.load_model(file_path)

                self.save_model_btn.setEnabled(True)
                self.status_label.setText("模型加载成功!")
                self.status_label.setStyleSheet(f"""
                    QLabel {{
                        color: {SUCCESS_COLOR};
                        font-size: 18px;
                        font-weight: bold;
                        padding: 15px;
                        background-color: {SECONDARY_BG};
                        border-radius: 8px;
                        border: 2px solid {SUCCESS_COLOR};
                    }}
                """)

                QMessageBox.information(self, "成功", "模型加载成功!")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载模型失败: {str(e)}")
