"""
深度学习检测模块
支持多种深度学习模型进行轴承故障分类检测
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QPushButton,
    QComboBox, QLineEdit, QTextEdit, QScrollArea, QFrame, QSplitter,
    QGroupBox, QProgressBar, QMessageBox, QFileDialog, QCheckBox,
    QTabWidget, QSpinBox, QDoubleSpinBox, QTableWidget, QTableWidgetItem,
    QHeaderView, QListWidget, QListWidgetItem, QSlider, QFormLayout
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor, QCursor

# 算法模块导入
from algorithms import create_deep_learning_classifier


from ui.styles import (
    PRIMARY_BG, SECONDARY_BG, ACCENT_COLOR, TEXT_PRIMARY, TEXT_SECONDARY,
    SUCCESS_COLOR, WARNING_COLOR, ERROR_COLOR, INFO_COLOR, HIGHLIGHT_COLOR
)


class ModelTrainingThread(QThread):
    """深度学习模型训练线程"""
    progress_updated = pyqtSignal(int, str)
    epoch_completed = pyqtSignal(int, float, float, float, float)
    training_completed = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)

    def __init__(self, X, y, architecture, model_config, test_size=0.2):
        super().__init__()
        self.X = X
        self.y = y
        self.architecture = architecture
        self.model_config = model_config
        self.test_size = test_size
        self.classifier = create_deep_learning_classifier()

        def run(self):
            """执行模型训练"""
            try:
                # 检查PyTorch是否可用
                if not self.classifier.is_available():
                    self.error_occurred.emit("PyTorch未安装，无法使用深度学习功能")
                    return

                # 进度回调函数
                def progress_callback(progress, message):
                    self.progress_updated.emit(progress, message)

                # epoch回调函数
                def epoch_callback(epoch, train_loss, train_acc, val_loss, val_acc):
                    self.epoch_completed.emit(epoch, train_loss, train_acc, val_loss, val_acc)

                # 准备数据
                self.progress_updated.emit(10, "准备数据...")
                X_train, X_test, y_train, y_test = self.classifier.prepare_data(
                    self.X, self.y, test_size=self.test_size
                )

                # 训练模型
                training_result = self.classifier.train(
                    X_train, y_train,
                    architecture=self.architecture,
                    config=self.model_config,
                    progress_callback=progress_callback,
                    epoch_callback=epoch_callback
                )

                # 评估模型
                self.progress_updated.emit(90, "评估模型...")
                evaluation_result = self.classifier.evaluate(X_test, y_test)

                # 合并结果
                results = {
                    'classifier': self.classifier,
                    'training_result': training_result,
                    'evaluation_result': evaluation_result
                }

                self.training_completed.emit(results)

            except Exception as e:
                self.error_occurred.emit(str(e))


class DeepLearningClassifier(QWidget):
    """深度学习分类器检测界面"""
    
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.current_classifier = None
        self.feature_data = None
        self.training_history = []
        
        # 模型配置选项
        self.model_configs = {
            "简单MLP": {
                "hidden_units": [128, 64],
                "dropout_rate": 0.3,
                "learning_rate": 0.001,
                "epochs": 100,
                "batch_size": 32
            },
            "深层MLP": {
                "hidden_units": [256, 128, 64, 32],
                "dropout_rate": 0.4,
                "learning_rate": 0.001,
                "epochs": 150,
                "batch_size": 32
            },
            "宽层MLP": {
                "hidden_units": [512, 256],
                "dropout_rate": 0.5,
                "learning_rate": 0.0005,
                "epochs": 100,
                "batch_size": 64
            }
        }
        
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        # 检查深度学习模块是否可用
        test_classifier = create_deep_learning_classifier()
        if not test_classifier.is_available():
            self.show_pytorch_warning()
            return
            
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("深度学习检测")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 28px;
                font-weight: bold;
                color: {ACCENT_COLOR};
                margin-bottom: 10px;
            }}
        """)
        layout.addWidget(title_label)
        
        # 创建主分割器
        main_splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(main_splitter)
        
        # 左侧控制面板
        self.create_control_panel(main_splitter)
        
        # 右侧结果显示区域
        self.create_result_panel(main_splitter)
        
        # 设置分割器比例
        main_splitter.setSizes([400, 800])
    
    def show_pytorch_warning(self):
        """显示PyTorch未安装警告"""
        layout = QVBoxLayout(self)
        layout.setAlignment(Qt.AlignCenter)

        warning_label = QLabel("⚠️ PyTorch未安装")
        warning_label.setAlignment(Qt.AlignCenter)
        warning_label.setStyleSheet(f"""
            font-size: 32px;
            color: {WARNING_COLOR};
            margin: 20px;
        """)
        layout.addWidget(warning_label)

        info_label = QLabel("请安装PyTorch以使用深度学习功能:\npip install torch torchvision")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet(f"""
            font-size: 18px;
            color: {TEXT_SECONDARY};
            margin: 20px;
        """)
        layout.addWidget(info_label)
        
    def create_control_panel(self, parent):
        """创建左侧控制面板"""
        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)
        control_layout.setContentsMargins(10, 10, 10, 10)
        control_layout.setSpacing(15)
        
        # 数据源选择
        data_group = QGroupBox("数据源选择")
        data_layout = QVBoxLayout(data_group)
        
        self.load_data_btn = QPushButton("加载特征数据")
        self.load_data_btn.clicked.connect(self.load_feature_data)
        self.load_data_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {INFO_COLOR};
                color: white;
                font-size: 16px;
                padding: 10px;
                border-radius: 8px;
                border: none;
            }}
            QPushButton:hover {{
                background-color: #5dade2;
            }}
        """)
        data_layout.addWidget(self.load_data_btn)
        
        self.data_status_label = QLabel("未加载数据")
        self.data_status_label.setStyleSheet(f"color: {TEXT_SECONDARY}; font-size: 14px;")
        data_layout.addWidget(self.data_status_label)
        
        control_layout.addWidget(data_group)
        
        # 网络架构选择
        arch_group = QGroupBox("网络架构")
        arch_layout = QFormLayout(arch_group)
        
        self.architecture_combo = QComboBox()
        self.architecture_combo.addItems(list(self.model_configs.keys()))
        self.architecture_combo.currentTextChanged.connect(self.update_model_config)
        arch_layout.addRow("架构类型:", self.architecture_combo)
        
        control_layout.addWidget(arch_group)

        # 训练参数配置
        params_group = QGroupBox("训练参数")
        params_layout = QFormLayout(params_group)

        self.epochs_spin = QSpinBox()
        self.epochs_spin.setRange(10, 1000)
        self.epochs_spin.setValue(100)
        params_layout.addRow("训练轮数:", self.epochs_spin)

        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(8, 256)
        self.batch_size_spin.setValue(32)
        params_layout.addRow("批次大小:", self.batch_size_spin)

        self.learning_rate_spin = QDoubleSpinBox()
        self.learning_rate_spin.setRange(0.0001, 0.1)
        self.learning_rate_spin.setDecimals(4)
        self.learning_rate_spin.setValue(0.001)
        params_layout.addRow("学习率:", self.learning_rate_spin)

        self.dropout_spin = QDoubleSpinBox()
        self.dropout_spin.setRange(0.0, 0.9)
        self.dropout_spin.setDecimals(2)
        self.dropout_spin.setValue(0.3)
        params_layout.addRow("Dropout率:", self.dropout_spin)

        control_layout.addWidget(params_group)

        # 数据预处理
        preprocess_group = QGroupBox("数据预处理")
        preprocess_layout = QVBoxLayout(preprocess_group)

        self.normalize_checkbox = QCheckBox("标准化特征")
        self.normalize_checkbox.setChecked(True)
        preprocess_layout.addWidget(self.normalize_checkbox)

        self.shuffle_checkbox = QCheckBox("随机打乱数据")
        self.shuffle_checkbox.setChecked(True)
        preprocess_layout.addWidget(self.shuffle_checkbox)

        control_layout.addWidget(preprocess_group)

        # 操作按钮
        button_layout = QVBoxLayout()

        self.train_btn = QPushButton("开始训练")
        self.train_btn.clicked.connect(self.train_model)
        self.train_btn.setEnabled(False)
        button_layout.addWidget(self.train_btn)

        self.stop_btn = QPushButton("停止训练")
        self.stop_btn.clicked.connect(self.stop_training)
        self.stop_btn.setEnabled(False)
        button_layout.addWidget(self.stop_btn)

        self.save_model_btn = QPushButton("保存模型")
        self.save_model_btn.clicked.connect(self.save_model)
        self.save_model_btn.setEnabled(False)
        button_layout.addWidget(self.save_model_btn)

        self.load_model_btn = QPushButton("加载模型")
        self.load_model_btn.clicked.connect(self.load_model)
        button_layout.addWidget(self.load_model_btn)

        control_layout.addWidget(QWidget())  # 占位符
        control_layout.addLayout(button_layout)

        parent.addWidget(control_widget)

        # 初始化模型配置
        self.update_model_config()

    def create_result_panel(self, parent):
        """创建右侧结果显示面板"""
        result_widget = QWidget()
        self.result_layout = QVBoxLayout(result_widget)
        self.result_layout.setContentsMargins(10, 10, 10, 10)
        self.result_layout.setSpacing(15)

        # 训练状态
        status_group = QGroupBox("训练状态")
        status_layout = QVBoxLayout(status_group)

        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_layout.addWidget(self.progress_bar)

        self.status_label = QLabel("等待开始训练...")
        self.status_label.setStyleSheet(f"color: {TEXT_SECONDARY}; font-size: 16px;")
        status_layout.addWidget(self.status_label)

        # 当前训练信息
        self.epoch_info_label = QLabel("")
        self.epoch_info_label.setStyleSheet(f"color: {TEXT_PRIMARY}; font-size: 14px;")
        status_layout.addWidget(self.epoch_info_label)

        self.result_layout.addWidget(status_group)

        # 结果显示区域（标签页）
        self.result_tabs = QTabWidget()

        # 训练曲线标签页
        self.training_curves_widget = QWidget()
        self.training_curves_layout = QVBoxLayout(self.training_curves_widget)
        self.result_tabs.addTab(self.training_curves_widget, "训练曲线")

        # 模型评估标签页
        self.evaluation_widget = QWidget()
        self.evaluation_layout = QVBoxLayout(self.evaluation_widget)
        self.result_tabs.addTab(self.evaluation_widget, "模型评估")

        # 网络结构标签页
        self.architecture_widget = QWidget()
        self.architecture_layout = QVBoxLayout(self.architecture_widget)
        self.result_tabs.addTab(self.architecture_widget, "网络结构")

        self.result_layout.addWidget(self.result_tabs)
        parent.addWidget(result_widget)

    def update_model_config(self):
        """更新模型配置"""
        arch_type = self.architecture_combo.currentText()
        config = self.model_configs[arch_type]

        self.epochs_spin.setValue(config['epochs'])
        self.batch_size_spin.setValue(config['batch_size'])
        self.dropout_spin.setValue(config['dropout_rate'])

    def load_feature_data(self):
        """加载特征数据"""
        try:
            # 这里应该从数据库加载特征数据
            # 暂时使用示例数据
            QMessageBox.information(self, "提示", "特征数据加载功能需要连接到特征提取模块")

            # 示例数据生成（实际应用中应从数据库读取）
            np.random.seed(42)
            n_samples = 2000
            n_features = 50

            # 生成示例特征数据
            X = np.random.randn(n_samples, n_features)
            # 生成示例标签（正常、内圈故障、外圈故障、滚动体故障）
            y = np.random.choice(['正常', '内圈故障', '外圈故障', '滚动体故障'], n_samples)

            self.feature_data = pd.DataFrame(X, columns=[f'特征_{i+1}' for i in range(n_features)])
            self.feature_data['标签'] = y

            self.data_status_label.setText(f"已加载 {len(self.feature_data)} 条数据")
            self.data_status_label.setStyleSheet(f"color: {SUCCESS_COLOR}; font-size: 14px;")
            self.train_btn.setEnabled(True)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载数据失败: {str(e)}")

    def train_model(self):
        """训练深度学习模型"""
        try:
            # 获取特征数据
            if self.feature_data is None:
                QMessageBox.warning(self, "警告", "请先加载特征数据")
                return

            X = self.feature_data.drop('标签', axis=1).values
            y = self.feature_data['标签'].values

            # 获取模型配置
            arch_type = self.architecture_combo.currentText()
            model_config = self.model_configs[arch_type].copy()
            model_config.update({
                'epochs': self.epochs_spin.value(),
                'batch_size': self.batch_size_spin.value(),
                'dropout_rate': self.dropout_spin.value(),
                'learning_rate': self.learning_rate_spin.value(),
                'normalize': self.normalize_checkbox.isChecked(),
                'shuffle': self.shuffle_checkbox.isChecked()
            })

            # 显示训练状态
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.status_label.setText("开始训练...")
            self.train_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)

            # 清空训练历史
            self.training_history = []

            # 启动训练线程
            self.training_thread = ModelTrainingThread(
                X, y, arch_type, model_config, test_size=0.2
            )
            self.training_thread.progress_updated.connect(self.update_training_progress)
            self.training_thread.epoch_completed.connect(self.on_epoch_completed)
            self.training_thread.training_completed.connect(self.on_training_completed)
            self.training_thread.error_occurred.connect(self.on_training_error)
            self.training_thread.start()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"训练失败: {str(e)}")
            self.train_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
            self.progress_bar.setVisible(False)

    def stop_training(self):
        """停止训练"""
        if hasattr(self, 'training_thread') and self.training_thread.isRunning():
            self.training_thread.terminate()
            self.training_thread.wait()

        self.train_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.status_label.setText("训练已停止")

    def update_training_progress(self, value, message):
        """更新训练进度"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)

    def on_epoch_completed(self, epoch, loss, accuracy, val_loss, val_accuracy):
        """处理每个epoch完成事件"""
        # 更新训练信息显示
        info_text = f"Epoch {epoch}: Loss={loss:.4f}, Acc={accuracy:.4f}, Val_Loss={val_loss:.4f}, Val_Acc={val_accuracy:.4f}"
        self.epoch_info_label.setText(info_text)

        # 记录训练历史
        self.training_history.append({
            'epoch': epoch,
            'loss': loss,
            'accuracy': accuracy,
            'val_loss': val_loss,
            'val_accuracy': val_accuracy
        })

        # 实时更新训练曲线
        self.update_training_curves()

    def on_training_completed(self, results):
        """训练完成处理"""
        self.current_classifier = results['classifier']
        self.progress_bar.setVisible(False)
        self.status_label.setText("训练完成!")
        self.train_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.save_model_btn.setEnabled(True)

        # 显示最终结果
        self.display_final_results(results)

    def on_training_error(self, error_message):
        """训练错误处理"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("训练失败")
        self.train_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        QMessageBox.critical(self, "训练错误", error_message)

    def update_training_curves(self):
        """更新训练曲线"""
        if not self.training_history:
            return

        # 清除现有图表
        while self.training_curves_layout.count():
            child = self.training_curves_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        # 创建matplotlib图形
        fig = Figure(figsize=(12, 8), facecolor=PRIMARY_BG)
        canvas = FigureCanvas(fig)

        # 损失曲线
        ax1 = fig.add_subplot(2, 1, 1)
        epochs = [h['epoch'] for h in self.training_history]
        train_loss = [h['loss'] for h in self.training_history]

        ax1.plot(epochs, train_loss, 'b-', label='训练损失', linewidth=2)
        ax1.set_title('训练损失曲线', fontsize=16, color=TEXT_PRIMARY)
        ax1.set_xlabel('Epoch', fontsize=12, color=TEXT_PRIMARY)
        ax1.set_ylabel('Loss', fontsize=12, color=TEXT_PRIMARY)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_facecolor(SECONDARY_BG)

        # 准确率曲线
        ax2 = fig.add_subplot(2, 1, 2)
        train_acc = [h['accuracy'] for h in self.training_history]

        ax2.plot(epochs, train_acc, 'b-', label='训练准确率', linewidth=2)
        ax2.set_title('训练准确率曲线', fontsize=16, color=TEXT_PRIMARY)
        ax2.set_xlabel('Epoch', fontsize=12, color=TEXT_PRIMARY)
        ax2.set_ylabel('Accuracy', fontsize=12, color=TEXT_PRIMARY)
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.set_facecolor(SECONDARY_BG)

        # 设置图表样式
        for ax in [ax1, ax2]:
            ax.tick_params(colors=TEXT_PRIMARY)

        fig.patch.set_facecolor(PRIMARY_BG)
        fig.tight_layout()

        self.training_curves_layout.addWidget(canvas)

    def display_final_results(self, results):
        """显示最终训练结果"""
        # 清除评估标签页
        while self.evaluation_layout.count():
            child = self.evaluation_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        # 获取评估结果
        eval_result = results['evaluation_result']
        training_result = results['training_result']

        # 性能指标表格
        metrics_table = QTableWidget(4, 2)
        metrics_table.setHorizontalHeaderLabels(["指标", "数值"])
        metrics_table.horizontalHeader().setStretchLastSection(True)

        metrics_data = [
            ("准确率", f"{eval_result['accuracy']:.4f}"),
            ("精确率", f"{eval_result['precision']:.4f}"),
            ("召回率", f"{eval_result['recall']:.4f}"),
            ("F1分数", f"{eval_result['f1_score']:.4f}")
        ]

        for i, (metric, value) in enumerate(metrics_data):
            metrics_table.setItem(i, 0, QTableWidgetItem(metric))
            metrics_table.setItem(i, 1, QTableWidgetItem(value))

        metrics_table.setMaximumHeight(200)
        self.evaluation_layout.addWidget(metrics_table)

        # 显示网络结构
        self.display_model_architecture()

        # 更新训练历史以便显示曲线
        if 'train_losses' in training_result and 'train_accuracies' in training_result:
            self.training_history = []
            for i, (loss, acc) in enumerate(zip(training_result['train_losses'], training_result['train_accuracies'])):
                self.training_history.append({
                    'epoch': i + 1,
                    'loss': loss,
                    'accuracy': acc
                })
            self.update_training_curves()

    def display_model_architecture(self):
        """显示模型架构"""
        # 清除架构标签页
        while self.architecture_layout.count():
            child = self.architecture_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        if self.current_classifier:
            # 创建模型摘要文本
            summary_text = QTextEdit()
            summary_text.setReadOnly(True)
            summary_text.setMaximumHeight(400)

            # 获取PyTorch模型摘要
            try:
                summary = str(self.current_model)
                summary_text.setText(summary)
            except:
                summary_text.setText("无法获取模型架构信息")

            self.architecture_layout.addWidget(summary_text)
        else:
            info_label = QLabel("模型架构信息将在训练完成后显示")
            info_label.setAlignment(Qt.AlignCenter)
            info_label.setStyleSheet(f"color: {TEXT_SECONDARY}; font-size: 16px;")
            self.architecture_layout.addWidget(info_label)

    def save_model(self):
        """保存训练好的模型"""
        if self.current_classifier is None:
            QMessageBox.warning(self, "警告", "没有可保存的模型")
            return

        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存模型", "", "模型文件 (*.pth);;所有文件 (*)"
            )

            if file_path:
                self.current_classifier.save_model(file_path)
                QMessageBox.information(self, "成功", "模型保存成功!")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存模型失败: {str(e)}")

    def load_model(self):
        """加载训练好的模型"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "加载模型", "", "模型文件 (*.pth);;所有文件 (*)"
            )

            if file_path:
                self.current_classifier = create_deep_learning_classifier()
                self.current_classifier.load_model(file_path)
                self.save_model_btn.setEnabled(True)
                QMessageBox.information(self, "成功", "模型加载成功!")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载模型失败: {str(e)}")
