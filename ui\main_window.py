"""
主窗口模块
"""

import sys
from PyQt5.QtWidgets import (QMainWindow, QWidget, QHBoxLayout, QVBoxLayout,
                             QStackedWidget, QLabel, QFrame, QApplication,
                             QStatusBar, QMenuBar, QSplitter, QMessageBox)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPixmap
from ui.styles import get_main_stylesheet, ACCENT_COLOR, TEXT_PRIMARY, TEXT_SECONDARY, PRIMARY_BG


class NavigationBar(QWidget):
    """导航栏组件"""
    page_changed = pyqtSignal(int)

    def __init__(self):
        super().__init__()
        self.current_index = 0
        self.init_ui()

    def init_ui(self):
        """初始化导航栏"""
        # 设置导航栏背景样式
        self.setStyleSheet(f"""
            NavigationBar {{
                background-color: {PRIMARY_BG};
                border-right: 2px solid #cccccc;
            }}
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # 导航项目
        nav_items = [
            ("🏠", "仪表盘"),
            ("📁", "文件选择"),
            ("📊", "特征提取与分析"),
            ("🔧", "故障判断"),
            ("🔍", "经典分类器监测"),
            ("🧠", "深度学习监测"),
            ("⚠️", "故障异常报警"),
            ("📋", "检测报告生成"),
            ("⚙️", "系统设置")
        ]

        self.nav_buttons = []
        for i, (icon, text) in enumerate(nav_items):
            button = self.create_nav_button(icon, text, i)
            self.nav_buttons.append(button)
            layout.addWidget(button)

        layout.addStretch()

        # 设置第一个按钮为选中状态
        if self.nav_buttons:
            self.nav_buttons[0].setProperty("selected", True)
            self.nav_buttons[0].setStyleSheet(self.get_nav_button_style(True))

    def create_nav_button(self, icon, text, index):
        """创建导航按钮"""
        button = QLabel()
        button.setText(f"{icon}  {text}")
        button.setStyleSheet(self.get_nav_button_style(False))
        button.mousePressEvent = lambda event, idx=index: self.on_nav_clicked(idx)
        button.setProperty("selected", False)
        return button

    def get_nav_button_style(self, selected):
        """获取导航按钮样式"""
        if selected:
            return f"""
                QLabel {{
                    background-color: {ACCENT_COLOR};
                    color: white;
                    padding: 25px 20px;
                    font-size: 22px;
                    font-weight: bold;
                    border-left: 5px solid {ACCENT_COLOR};
                    font-family: 'Microsoft YaHei';
                }}
            """
        else:
            return f"""
                QLabel {{
                    background-color: transparent;
                    color: {TEXT_SECONDARY};
                    padding: 25px 20px;
                    font-size: 22px;
                    border-left: 5px solid transparent;
                    font-family: 'Microsoft YaHei';
                }}
                QLabel:hover {{
                    background-color: #a0e0ff;
                    color: {TEXT_PRIMARY};
                    border-left: 5px solid #0066cc;
                }}
            """

    def on_nav_clicked(self, index):
        """导航点击事件"""
        if index != self.current_index:
            # 重置之前选中的按钮
            if 0 <= self.current_index < len(self.nav_buttons):
                self.nav_buttons[self.current_index].setProperty("selected", False)
                self.nav_buttons[self.current_index].setStyleSheet(self.get_nav_button_style(False))

            # 设置新选中的按钮
            self.current_index = index
            self.nav_buttons[index].setProperty("selected", True)
            self.nav_buttons[index].setStyleSheet(self.get_nav_button_style(True))

            # 发送页面切换信号
            self.page_changed.emit(index)

    def set_current_page(self, index):
        """设置当前页面"""
        if 0 <= index < len(self.nav_buttons):
            self.on_nav_clicked(index)


class MainWindow(QMainWindow):
    """主窗口类"""

    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.init_ui()
        self.setup_menu()
        self.setup_status_bar()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("地面数据分析决策系统")
        self.setGeometry(50, 50, 1280, 930)  # 增加窗口高度为菜单栏留出空间
        self.setFixedSize(1280, 930)  # 固定窗口大小，禁止调整

        # 应用样式表
        self.setStyleSheet(get_main_stylesheet())

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局 - 为菜单栏留出空间
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)  # 不设置顶部边距，让菜单栏显示
        main_layout.setSpacing(0)

        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #cccccc;
                width: 3px;
            }
            QSplitter::handle:hover {
                background-color: #0066cc;
            }
        """)

        # 左侧导航栏
        self.navigation = NavigationBar()
        self.navigation.setFixedWidth(280)  # 减小导航栏宽度
        self.navigation.page_changed.connect(self.on_page_changed)
        splitter.addWidget(self.navigation)

        # 右侧工作区容器
        workspace_container = QWidget()
        workspace_layout = QVBoxLayout(workspace_container)
        workspace_layout.setContentsMargins(0, 0, 0, 0)
        workspace_layout.setSpacing(0)

        # 顶部logo区域
        self.create_logo_area(workspace_layout)

        # 工作区
        self.workspace = QStackedWidget()
        self.workspace.setStyleSheet(f"""
            QStackedWidget {{
                background-color: {PRIMARY_BG};
                border-left: 2px solid #cccccc;
            }}
        """)
        workspace_layout.addWidget(self.workspace)

        splitter.addWidget(workspace_container)

        # 设置分割器比例
        splitter.setSizes([280, 1000])  # 调整分割比例适应新的导航栏宽度
        splitter.setCollapsible(0, False)  # 导航栏不可折叠
        splitter.setCollapsible(1, False)  # 工作区不可折叠

        main_layout.addWidget(splitter)

        # 添加页面
        self.setup_pages()

        # 设置默认页面
        self.workspace.setCurrentIndex(0)
        self.navigation.set_current_page(0)

    def create_logo_area(self, parent_layout):
        """创建顶部区域，包含菜单栏和logo"""
        # 顶部容器
        top_container = QWidget()
        top_container.setFixedHeight(80)
        top_container.setStyleSheet(f"""
            QWidget {{
                background-color: {PRIMARY_BG};
                border-left: 2px solid #cccccc;
                border-bottom: 1px solid #cccccc;
            }}
        """)

        top_layout = QHBoxLayout(top_container)
        top_layout.setContentsMargins(20, 10, 20, 10)
        top_layout.setSpacing(15)

        # 左侧菜单栏区域
        menu_container = QWidget()
        menu_layout = QHBoxLayout(menu_container)
        menu_layout.setContentsMargins(0, 0, 0, 0)
        menu_layout.setSpacing(0)

        # 创建自定义菜单栏
        self.create_custom_menu_bar(menu_layout)

        top_layout.addWidget(menu_container)

        # 中间弹性空间
        top_layout.addStretch()

        # 公司标注 - 文字在右边
        company_label = QLabel("解放军七四三五工厂")
        company_label.setStyleSheet(f"""
            QLabel {{
                font-size: 20px;
                font-weight: bold;
                color: {TEXT_PRIMARY} !important;
                font-family: 'Microsoft YaHei', 'SimHei', 'Arial Unicode MS', sans-serif;
                background-color: transparent;
                padding: 5px 10px;
                min-width: 180px;
                max-width: 200px;
            }}
        """)
        company_label.setAlignment(Qt.AlignCenter)
        company_label.setVisible(True)
        top_layout.addWidget(company_label)

        # logo图标 - 在最右边，使用您指定的图标
        logo_icon = QLabel()
        logo_icon.setAlignment(Qt.AlignCenter)
        logo_icon.setFixedSize(50, 50)

        # 加载logo图片
        import os
        logo_path = os.path.join(os.path.dirname(__file__), 'logo.png')
        if os.path.exists(logo_path):
            pixmap = QPixmap(logo_path)
            # 缩放图片以适应标签大小，保持宽高比
            scaled_pixmap = pixmap.scaled(50, 50, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            logo_icon.setPixmap(scaled_pixmap)
        else:
            # 如果图片不存在，显示文字作为备选
            logo_icon.setText("LOGO")
            logo_icon.setStyleSheet(f"""
                QLabel {{
                    font-size: 16px;
                    font-weight: bold;
                    color: {ACCENT_COLOR};
                    background-color: transparent;
                    border: 2px solid {ACCENT_COLOR};
                    border-radius: 25px;
                    padding: 5px;
                }}
            """)

        logo_icon.setStyleSheet(f"""
            QLabel {{
                background-color: transparent;
                border: none;
                padding: 5px;
            }}
        """)
        top_layout.addWidget(logo_icon)

        parent_layout.addWidget(top_container)

    def create_custom_menu_bar(self, parent_layout):
        """创建自定义菜单栏"""
        from PyQt5.QtWidgets import QPushButton, QMenu

        # 文件菜单按钮
        file_button = QPushButton("文件")
        file_button.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                color: {TEXT_PRIMARY};
                border: none;
                padding: 8px 16px;
                font-size: 16px;
                font-family: 'Microsoft YaHei';
                border-radius: 4px;
            }}
            QPushButton:hover {{
                background-color: {ACCENT_COLOR};
                color: white;
            }}
        """)

        # 创建文件菜单
        file_menu = QMenu(file_button)
        file_menu.setStyleSheet(f"""
            QMenu {{
                background-color: {PRIMARY_BG};
                color: {TEXT_PRIMARY};
                border: 1px solid #cccccc;
                border-radius: 6px;
                font-size: 14px;
                font-family: 'Microsoft YaHei';
            }}
            QMenu::item {{
                padding: 8px 20px;
                border-radius: 4px;
                margin: 2px;
            }}
            QMenu::item:hover {{
                background-color: {ACCENT_COLOR};
                color: white;
            }}
        """)

        # 添加文件菜单项
        import_action = file_menu.addAction('导入TDMS文件')
        import_action.setShortcut('Ctrl+I')
        import_action.triggered.connect(self.import_data)

        export_action = file_menu.addAction('导出报告')
        export_action.setShortcut('Ctrl+E')
        export_action.triggered.connect(self.export_report)

        file_menu.addSeparator()

        exit_action = file_menu.addAction('退出')
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)

        file_button.setMenu(file_menu)
        parent_layout.addWidget(file_button)

        # 工具菜单按钮
        tools_button = QPushButton("工具")
        tools_button.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                color: {TEXT_PRIMARY};
                border: none;
                padding: 8px 16px;
                font-size: 16px;
                font-family: 'Microsoft YaHei';
                border-radius: 4px;
            }}
            QPushButton:hover {{
                background-color: {ACCENT_COLOR};
                color: white;
            }}
        """)

        # 创建工具菜单
        tools_menu = QMenu(tools_button)
        tools_menu.setStyleSheet(f"""
            QMenu {{
                background-color: {PRIMARY_BG};
                color: {TEXT_PRIMARY};
                border: 1px solid #cccccc;
                border-radius: 6px;
                font-size: 14px;
                font-family: 'Microsoft YaHei';
            }}
            QMenu::item {{
                padding: 8px 20px;
                border-radius: 4px;
                margin: 2px;
            }}
            QMenu::item:hover {{
                background-color: {ACCENT_COLOR};
                color: white;
            }}
        """)

        # 添加工具菜单项
        db_action = tools_menu.addAction('数据库连接设置')
        db_action.triggered.connect(self.show_db_settings)

        settings_action = tools_menu.addAction('系统设置')
        settings_action.triggered.connect(self.show_settings)

        tools_button.setMenu(tools_menu)
        parent_layout.addWidget(tools_button)

        # 帮助菜单按钮
        help_button = QPushButton("帮助")
        help_button.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                color: {TEXT_PRIMARY};
                border: none;
                padding: 8px 16px;
                font-size: 16px;
                font-family: 'Microsoft YaHei';
                border-radius: 4px;
            }}
            QPushButton:hover {{
                background-color: {ACCENT_COLOR};
                color: white;
            }}
        """)

        # 创建帮助菜单
        help_menu = QMenu(help_button)
        help_menu.setStyleSheet(f"""
            QMenu {{
                background-color: {PRIMARY_BG};
                color: {TEXT_PRIMARY};
                border: 1px solid #cccccc;
                border-radius: 6px;
                font-size: 14px;
                font-family: 'Microsoft YaHei';
            }}
            QMenu::item {{
                padding: 8px 20px;
                border-radius: 4px;
                margin: 2px;
            }}
            QMenu::item:hover {{
                background-color: {ACCENT_COLOR};
                color: white;
            }}
        """)

        # 添加帮助菜单项
        about_action = help_menu.addAction('关于')
        about_action.triggered.connect(self.show_about)

        help_button.setMenu(help_menu)
        parent_layout.addWidget(help_button)

    def setup_pages(self):
        """设置工作区页面"""
        from ui.dashboard import Dashboard
        from ui.file_selector import FileSelector
        from ui.feature_analysis import FeatureAnalysis
        from ui.fault_diagnosis import FaultDiagnosis
        from ui.classical_classifier import ClassicalClassifier
        from ui.deep_learning_classifier import DeepLearningClassifier

        # 仪表盘页面
        dashboard = Dashboard(self.db_manager)
        self.workspace.addWidget(dashboard)

        # TDMS文件选择页面
        self.file_selector = FileSelector(self.db_manager)
        self.workspace.addWidget(self.file_selector)

        # 特征提取与分析页面
        self.feature_analysis = FeatureAnalysis(self.db_manager)
        self.workspace.addWidget(self.feature_analysis)

        # 故障判断页面
        self.fault_diagnosis = FaultDiagnosis(self.db_manager)
        self.workspace.addWidget(self.fault_diagnosis)

        # 建立文件选择器和特征分析模块之间的连接
        self.feature_analysis.set_file_selector_connection(self.file_selector)

        # 经典分类器监测页面
        self.classical_classifier = ClassicalClassifier(self.db_manager)
        self.workspace.addWidget(self.classical_classifier)

        # 深度学习分类器监测页面
        self.deep_learning_classifier = DeepLearningClassifier(self.db_manager)
        self.workspace.addWidget(self.deep_learning_classifier)

        # 故障异常报警页面
        alarm_system = self.create_placeholder_page("故障异常报警", "此功能正在开发中...")
        self.workspace.addWidget(alarm_system)

        # 检测报告生成页面
        report_generator = self.create_placeholder_page("检测报告生成", "此功能正在开发中...")
        self.workspace.addWidget(report_generator)

        # 系统设置页面
        system_settings = self.create_placeholder_page("系统设置", "此功能正在开发中...")
        self.workspace.addWidget(system_settings)

    def create_placeholder_page(self, title, description):
        """创建占位符页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setAlignment(Qt.AlignCenter)
        layout.setContentsMargins(40, 40, 40, 40)  # 增加内容边距

        # 标题
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"""
            font-size: 48px;
            font-weight: bold;
            color: {ACCENT_COLOR};
            margin: 40px;
            padding: 20px;
            font-family: 'Microsoft YaHei';
        """)
        layout.addWidget(title_label)

        # 描述
        desc_label = QLabel(description)
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet(f"""
            font-size: 32px;
            color: {TEXT_SECONDARY};
            margin: 30px;
            padding: 20px;
            font-family: 'Microsoft YaHei';
        """)
        layout.addWidget(desc_label)

        return page

    def setup_menu(self):
        """设置菜单栏 - 现在菜单栏已集成到顶部区域"""
        # 隐藏默认菜单栏，因为我们已经在顶部区域创建了自定义菜单栏
        menubar = self.menuBar()
        menubar.setVisible(False)
        menubar.hide()

    def setup_status_bar(self):
        """设置状态栏"""
        status_bar = QStatusBar()
        status_bar.setStyleSheet(f"""
            QStatusBar {{
                background-color: {PRIMARY_BG};
                color: {TEXT_PRIMARY};
                font-size: 20px;
                padding: 8px;
                font-family: 'Microsoft YaHei';
                border-top: 2px solid #cccccc;
            }}
        """)
        self.setStatusBar(status_bar)

        # 数据库连接状态
        self.db_status_label = QLabel("数据库: 已连接")
        self.db_status_label.setStyleSheet(f"""
            color: {TEXT_SECONDARY};
            padding: 12px 20px;
            font-size: 20px;
            font-family: 'Microsoft YaHei';
        """)
        status_bar.addWidget(self.db_status_label)

        separator1 = QLabel("|")
        separator1.setStyleSheet(f"color: {TEXT_SECONDARY}; font-size: 20px;")
        status_bar.addPermanentWidget(separator1)

        # 当前用户
        self.user_label = QLabel("用户: admin")
        self.user_label.setStyleSheet(f"""
            color: {TEXT_SECONDARY};
            padding: 12px 20px;
            font-size: 20px;
            font-family: 'Microsoft YaHei';
        """)
        status_bar.addPermanentWidget(self.user_label)

        separator2 = QLabel("|")
        separator2.setStyleSheet(f"color: {TEXT_SECONDARY}; font-size: 20px;")
        status_bar.addPermanentWidget(separator2)

        # 系统时间
        self.time_label = QLabel()
        self.time_label.setStyleSheet(f"""
            color: {TEXT_SECONDARY};
            padding: 12px 20px;
            font-size: 20px;
            font-family: 'Microsoft YaHei';
        """)
        status_bar.addPermanentWidget(self.time_label)

        # 启动时间更新定时器
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)  # 每秒更新一次
        self.update_time()

    def update_time(self):
        """更新时间显示"""
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(f"时间: {current_time}")

    def on_page_changed(self, index):
        """页面切换事件"""
        self.workspace.setCurrentIndex(index)

        # 更新状态栏信息
        page_names = [
            "仪表盘", "文件选择", "特征提取", "故障判断", "经典分类器",
            "深度学习", "故障报警", "报告生成", "系统设置"
        ]
        if 0 <= index < len(page_names):
            self.statusBar().showMessage(f"当前页面: {page_names[index]}", 3000)

    def import_data(self):
        """导入数据"""
        self.statusBar().showMessage("导入数据功能正在开发中...", 3000)

    def export_report(self):
        """导出报告"""
        self.statusBar().showMessage("导出报告功能正在开发中...", 3000)

    def show_db_settings(self):
        """显示数据库设置"""
        self.statusBar().showMessage("数据库设置功能正在开发中...", 3000)

    def show_settings(self):
        """显示系统设置"""
        self.statusBar().showMessage("系统设置功能正在开发中...", 3000)

    def show_about(self):
        """显示关于信息"""
        QMessageBox.about(self, "关于",
                         "地面数据分析决策系统 v1.0\n\n"
                         "轴承故障诊断专业版\n"
                         "基于PyQt5开发")

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 断开数据库连接
        if self.db_manager:
            self.db_manager.disconnect()
        event.accept()


if __name__ == "__main__":
    app = QApplication(sys.argv)

    # 这里需要数据库管理器实例
    from database.db_manager import DatabaseManager
    db_manager = DatabaseManager()

    window = MainWindow(db_manager)
    window.show()
    sys.exit(app.exec_())
